<!DOCTYPE html>
<html>
<head>
  <title>Handle onerror </title>
</head>
<body>
  <h2>Open the console to see error handling</h2>

  <script>
     Declarative Function
    // window.onerror = handleError;

    // function handleError(message, source, lineno, colno, error) {
    //   alert("Error (Declarative Function)!");
    //   console.log("info", message);
    // }

     
    window.onerror = function(message, source, lineno, colno, error) {
      alert("ًError (Function Expression)!");
      console.log("info :", message);
    };

    //  test
    notDefinedFunction();
  </script>

  
</body>
</html>
